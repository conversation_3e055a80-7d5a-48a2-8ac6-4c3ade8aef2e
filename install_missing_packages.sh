#!/bin/bash

# 安装缺失的包到conda law-gpt环境
# 使用conda环境的pip来安装

CONDA_ENV_PATH="/Users/<USER>/miniconda3/envs/law-gpt"
PIP_PATH="$CONDA_ENV_PATH/bin/pip"

echo "🚀 开始安装缺失的包到conda law-gpt环境..."

# 检查conda环境是否存在
if [ ! -d "$CONDA_ENV_PATH" ]; then
    echo "❌ conda law-gpt环境不存在，请先创建环境"
    exit 1
fi

echo "📦 安装Core Framework包..."
$PIP_PATH install fastapi==0.104.1
$PIP_PATH install "uvicorn[standard]==0.24.0"
$PIP_PATH install pydantic-settings==2.1.0

echo "📦 安装LangChain包..."
$PIP_PATH install langchain==0.1.0
$PIP_PATH install langchain-community==0.0.10
$PIP_PATH install langchain-core==0.1.0

echo "📦 安装LlamaIndex额外包..."
$PIP_PATH install llama-index-retrievers-bm25==0.1.3
$PIP_PATH install llama-index-postprocessor-rankgpt-rerank==0.1.3
$PIP_PATH install llama-index-postprocessor-cohere-rerank==0.1.7

echo "📦 安装Vector Database包..."
$PIP_PATH install faiss-cpu==1.7.4

echo "📦 安装Database包..."
$PIP_PATH install psycopg2-binary==2.9.9
$PIP_PATH install alembic==1.13.1

echo "📦 安装Cache包..."
$PIP_PATH install redis==5.0.1
$PIP_PATH install hiredis==2.2.3

echo "📦 安装Document Processing包..."
$PIP_PATH install python-docx==1.1.0
$PIP_PATH install python-multipart==0.0.6

echo "📦 安装Utilities包..."
$PIP_PATH install aiofiles==23.2.1

echo "📦 安装Development and Testing包..."
$PIP_PATH install pytest==7.4.3
$PIP_PATH install pytest-asyncio==0.21.1
$PIP_PATH install black==23.11.0
$PIP_PATH install isort==5.12.0
$PIP_PATH install flake8==6.1.0

echo "📦 安装Monitoring包..."
$PIP_PATH install prometheus-client==0.19.0

echo "✅ 所有包安装完成！"

# 验证安装
echo "🔍 验证关键包是否安装成功..."
$PIP_PATH list | grep -E "(fastapi|langchain|faiss|redis|pytest)"

echo "📋 当前环境包列表已保存到 conda_packages.txt"
$PIP_PATH list > conda_packages.txt
