"""
RAG配置管理
支持不同的RAG策略和最佳实践配置
"""
from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any, Optional


class RAGFramework(Enum):
    """RAG框架枚举"""
    LANGCHAIN = "langchain"
    LLAMAINDEX = "llamaindex"
    ADVANCED = "advanced"


class VectorStoreType(Enum):
    """向量存储类型"""
    FAISS = "faiss"
    MILVUS = "milvus"
    CHROMA = "chroma"
    PINECONE = "pinecone"


class RAGStrategy(Enum):
    """RAG策略枚举"""
    NAIVE = "naive"
    HYBRID = "hybrid"
    SELF_RAG = "self_rag"
    CORRECTIVE = "corrective"
    MULTI_HOP = "multi_hop"


@dataclass
class RAGConfig:
    """RAG配置类"""
    # 基础配置
    framework: RAGFramework = RAGFramework.ADVANCED
    vector_store: VectorStoreType = VectorStoreType.FAISS
    strategy: RAGStrategy = RAGStrategy.HYBRID
    
    # 检索配置
    top_k: int = 10
    similarity_threshold: float = 0.7
    max_context_length: int = 4000
    
    # 分块配置
    chunk_size: int = 512
    chunk_overlap: int = 50
    
    # 高级特性开关
    enable_reranking: bool = True
    enable_query_rewriting: bool = True
    enable_context_compression: bool = True
    enable_hybrid_search: bool = True
    
    # 混合检索权重
    vector_weight: float = 0.7  # 向量检索权重
    bm25_weight: float = 0.3   # BM25检索权重
    
    # Self-RAG配置
    self_rag_threshold: float = 7.0  # 自我评估阈值
    
    # 缓存配置
    enable_cache: bool = True
    cache_ttl: int = 3600  # 缓存过期时间(秒)


class RAGConfigManager:
    """RAG配置管理器"""
    
    # 预定义配置
    CONFIGS = {
        "production": RAGConfig(
            framework=RAGFramework.ADVANCED,
            strategy=RAGStrategy.HYBRID,
            top_k=5,
            similarity_threshold=0.75,
            enable_reranking=True,
            enable_query_rewriting=True,
            enable_context_compression=True,
            enable_cache=True
        ),
        
        "development": RAGConfig(
            framework=RAGFramework.ADVANCED,
            strategy=RAGStrategy.NAIVE,
            top_k=3,
            similarity_threshold=0.6,
            enable_reranking=False,
            enable_query_rewriting=False,
            enable_context_compression=False,
            enable_cache=False
        ),
        
        "high_accuracy": RAGConfig(
            framework=RAGFramework.ADVANCED,
            strategy=RAGStrategy.SELF_RAG,
            top_k=10,
            similarity_threshold=0.8,
            enable_reranking=True,
            enable_query_rewriting=True,
            enable_context_compression=True,
            self_rag_threshold=8.0,
            enable_cache=True
        ),
        
        "fast_response": RAGConfig(
            framework=RAGFramework.ADVANCED,
            strategy=RAGStrategy.NAIVE,
            top_k=3,
            similarity_threshold=0.6,
            enable_reranking=False,
            enable_query_rewriting=False,
            enable_context_compression=False,
            enable_cache=True
        ),
        
        "complex_queries": RAGConfig(
            framework=RAGFramework.ADVANCED,
            strategy=RAGStrategy.CORRECTIVE,
            top_k=8,
            similarity_threshold=0.7,
            enable_reranking=True,
            enable_query_rewriting=True,
            enable_context_compression=True,
            enable_cache=True
        )
    }
    
    @classmethod
    def get_config(cls, config_name: str = "production") -> RAGConfig:
        """获取指定配置"""
        return cls.CONFIGS.get(config_name, cls.CONFIGS["production"])
    
    @classmethod
    def get_available_configs(cls) -> Dict[str, Dict[str, Any]]:
        """获取所有可用配置的描述"""
        return {
            "production": {
                "description": "生产环境配置，平衡性能和准确性",
                "use_case": "生产部署，日常查询",
                "features": ["混合检索", "重排序", "查询重写", "上下文压缩", "缓存"]
            },
            "development": {
                "description": "开发环境配置，快速调试",
                "use_case": "开发调试，快速测试",
                "features": ["简单检索", "无缓存"]
            },
            "high_accuracy": {
                "description": "高准确性配置，适合重要查询",
                "use_case": "法律咨询，重要决策",
                "features": ["Self-RAG", "高阈值", "全功能启用"]
            },
            "fast_response": {
                "description": "快速响应配置，适合实时场景",
                "use_case": "实时聊天，快速问答",
                "features": ["简单检索", "低延迟", "缓存优化"]
            },
            "complex_queries": {
                "description": "复杂查询配置，适合多轮对话",
                "use_case": "复杂法律问题，多轮对话",
                "features": ["Corrective RAG", "查询扩展", "全功能启用"]
            }
        }
    
    @classmethod
    def create_custom_config(
        cls,
        strategy: RAGStrategy = RAGStrategy.HYBRID,
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        **kwargs
    ) -> RAGConfig:
        """创建自定义配置"""
        config = RAGConfig(
            strategy=strategy,
            top_k=top_k,
            similarity_threshold=similarity_threshold
        )
        
        # 更新其他参数
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        return config


# 全局配置实例
rag_config_manager = RAGConfigManager()


def get_rag_config(environment: str = "production") -> RAGConfig:
    """获取RAG配置"""
    return rag_config_manager.get_config(environment)


def get_config_recommendations() -> Dict[str, str]:
    """获取配置推荐"""
    return {
        "小型项目 (<1000文档)": "fast_response",
        "中型项目 (1000-10000文档)": "production", 
        "大型项目 (>10000文档)": "high_accuracy",
        "实时聊天应用": "fast_response",
        "法律咨询系统": "high_accuracy",
        "知识问答系统": "production",
        "复杂分析任务": "complex_queries",
        "开发测试": "development"
    }
