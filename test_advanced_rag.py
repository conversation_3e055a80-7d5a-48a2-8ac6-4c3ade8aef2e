#!/usr/bin/env python3
"""
测试高级RAG服务
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.connection import db_manager
from app.models.schemas import QueryRequest
from app.services.advanced_rag_service import advanced_rag_service, RAGStrategy, create_advanced_rag_service


async def test_advanced_rag():
    """测试高级RAG服务"""
    print("🚀 开始测试高级RAG服务...")
    
    # 获取数据库连接
    db = next(db_manager.get_db())
    
    try:
        # 1. 测试服务初始化
        print("\n📋 1. 测试服务初始化...")
        stats = advanced_rag_service.get_index_stats()
        print(f"   框架: {stats.get('framework', 'Unknown')}")
        print(f"   策略: {stats.get('strategy', 'Unknown')}")
        print(f"   特性: {stats.get('features', {})}")
        
        # 2. 加载文档到索引
        print("\n📚 2. 加载文档到索引...")
        try:
            advanced_rag_service.load_documents_from_database(db)
            print("   ✅ 文档加载成功")
        except Exception as e:
            print(f"   ❌ 文档加载失败: {e}")
            return False
        
        # 3. 测试不同的RAG策略
        test_questions = [
            "什么是合同法？",
            "如何处理劳动纠纷？",
            "公司法的基本原则是什么？"
        ]
        
        strategies = [
            (RAGStrategy.HYBRID, "混合RAG"),
            (RAGStrategy.SELF_RAG, "Self-RAG"),
            (RAGStrategy.CORRECTIVE, "Corrective RAG"),
            (RAGStrategy.NAIVE, "简单RAG")
        ]
        
        for strategy, strategy_name in strategies:
            print(f"\n🔍 3. 测试{strategy_name}策略...")
            
            # 创建特定策略的服务
            strategy_service = create_advanced_rag_service(strategy)
            
            # 加载文档
            try:
                strategy_service.load_documents_from_database(db)
            except Exception as e:
                print(f"   ❌ {strategy_name}文档加载失败: {e}")
                continue
            
            # 测试查询
            for question in test_questions[:1]:  # 只测试第一个问题以节省时间
                try:
                    request = QueryRequest(
                        question=question,
                        user_id="test_user",
                        top_k=3
                    )
                    
                    response = await strategy_service.query(request, db)
                    
                    print(f"   问题: {question}")
                    print(f"   回答: {response.answer[:100]}...")
                    print(f"   置信度: {response.confidence_score:.2f}")
                    print(f"   响应时间: {response.response_time:.2f}s")
                    print(f"   检索文档数: {len(response.retrieved_documents)}")
                    print(f"   ✅ {strategy_name}测试成功")
                    break
                    
                except Exception as e:
                    print(f"   ❌ {strategy_name}查询失败: {e}")
                    continue
        
        # 4. 性能对比测试
        print(f"\n⚡ 4. 性能对比测试...")
        
        test_question = "什么是合同法？"
        request = QueryRequest(
            question=test_question,
            user_id="performance_test",
            top_k=5
        )
        
        performance_results = {}
        
        for strategy, strategy_name in strategies:
            try:
                strategy_service = create_advanced_rag_service(strategy)
                strategy_service.load_documents_from_database(db)
                
                # 执行查询
                response = await strategy_service.query(request, db)
                
                performance_results[strategy_name] = {
                    "response_time": response.response_time,
                    "confidence": response.confidence_score,
                    "retrieved_docs": len(response.retrieved_documents)
                }
                
                print(f"   {strategy_name}: {response.response_time:.2f}s, 置信度: {response.confidence_score:.2f}")
                
            except Exception as e:
                print(f"   ❌ {strategy_name}性能测试失败: {e}")
                performance_results[strategy_name] = {"error": str(e)}
        
        # 5. 显示最佳策略推荐
        print(f"\n🏆 5. 策略推荐...")
        
        valid_results = {k: v for k, v in performance_results.items() if "error" not in v}
        
        if valid_results:
            # 按置信度排序
            best_confidence = max(valid_results.items(), key=lambda x: x[1]["confidence"])
            print(f"   最高置信度: {best_confidence[0]} ({best_confidence[1]['confidence']:.2f})")
            
            # 按速度排序
            fastest = min(valid_results.items(), key=lambda x: x[1]["response_time"])
            print(f"   最快响应: {fastest[0]} ({fastest[1]['response_time']:.2f}s)")
            
            # 综合评分 (置信度 * 0.7 + 速度权重 * 0.3)
            scored_results = {}
            max_time = max(v["response_time"] for v in valid_results.values())
            
            for name, result in valid_results.items():
                speed_score = 1 - (result["response_time"] / max_time)  # 速度越快分数越高
                composite_score = result["confidence"] * 0.7 + speed_score * 0.3
                scored_results[name] = composite_score
            
            best_overall = max(scored_results.items(), key=lambda x: x[1])
            print(f"   综合最佳: {best_overall[0]} (评分: {best_overall[1]:.2f})")
        
        print(f"\n✅ 高级RAG服务测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False
    
    finally:
        db.close()


async def main():
    """主函数"""
    print("🔧 高级RAG服务测试工具")
    print("=" * 50)
    
    success = await test_advanced_rag()
    
    if success:
        print("\n🎉 所有测试通过！高级RAG服务可以正常使用。")
        print("\n📝 使用建议:")
        print("   1. 混合RAG适合大多数场景，平衡了准确性和性能")
        print("   2. Self-RAG适合对准确性要求极高的场景")
        print("   3. Corrective RAG适合复杂查询和多轮对话")
        print("   4. 简单RAG适合快速响应的场景")
        return 0
    else:
        print("\n❌ 测试失败，请检查配置和依赖")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
